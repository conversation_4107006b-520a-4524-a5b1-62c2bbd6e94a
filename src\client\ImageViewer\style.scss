.image-viewer {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--bs-body-bg);
  color: var(--bs-body-color);
}

.image-viewer-header {
  padding: 12px 16px;
  border-bottom: 1px solid var(--bs-border-color);
  background-color: var(--bs-tertiary-bg);
}

.image-viewer-content {
  flex: 1;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: auto;
}

.image-viewer-img {
  max-width: 100%;
  max-height: 100%;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  object-fit: contain;
}

.image-viewer-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--bs-text-muted);
  font-family: var(--bs-font-monospace);
  font-size: 14px;
}
