export const CONFIG = {
  NUM_STARS: 800,
  MIN_STAR_SIZE: 1,
  MAX_STAR_SIZE: 12,
  MIN_SPEED: 0.2,
  MAX_SPEED: 6,
  PERSPECTIVE_FACTOR: 0.005,
  ROTATION_SPEED: 0.01,
  BG_COLOR: 'rgba(10, 10, 20, 0.05)',
  SIZE_DISTRIBUTION: [0.6, 0.3, 0.1], // small, medium, large stars
  WIGGLE_AMPLITUDE: 50, // How far stars wiggle from their base position
  WIGGLE_FREQUENCY: 0.01, // How fast the wiggle oscillates
  WIGGLE_PHASE_VARIATION: 2, // Creates different wiggle patterns for each star
  HUE_SHIFT_SPEED: 0.5, // How fast the hue shifts (degrees per frame)
};
