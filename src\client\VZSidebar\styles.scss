.vz-sidebar {
  height: 100%;
  color: var(--vh-color-neutral-04);
  background: var(--vh-color-neutral-01);
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .full-box {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    justify-content: start;
    align-items: start;
    border-bottom: 2px solid var(--vh-color-neutral-02);
    overflow: hidden;
  }

  .files {
    margin-top: 6px;
    display: flex;
    flex-direction: row;
    justify-content: start;
    align-items: start;
    flex: 1;
    outline: none;
    overflow: auto;
    height: 100%;
  }

  .file-or-directory {
    min-height: 40px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    margin: 0 6px;
    padding: 0 4px 0 8px;
    border-radius: 8px;
    font-family: var(--vzcode-font-family);
    font-weight: 500;
  }

  .sidebar-section-hint {
    margin-left: 10px;
    font-size: 12px;
    font-weight: 500;
    font-family: Poppins;
    color: var(--vh-color-neutral-04);
  }

  .sidebar-section-buttons {
    border-right: 2px solid var(--vh-color-neutral-02);
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 6px;
    padding: 6px;

    a {
      color: inherit;
    }
  }

  .sidebar-files,
  .sidebar-search,
  .sidebar-ai-chat,
  .sidebar-visual-editor {
    width: 100%;
  }

  .sidebar-search {
    * {
      color: inherit;
    }
  }

  .sidebar-ai-chat {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .sidebar-search .arrow-wrapper {
    cursor: pointer;
  }

  .sidebar-search-form {
    padding: 10px;
  }

  .search-file-heading {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    cursor: pointer;

    * {
      margin: 0px;
    }
  }

  .search-file-title,
  .search-file-name,
  .search-file-info {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
  }

  .search-file-name {
    font-size: inherit;
  }

  .search-file-count {
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0px;
    font-size: 12px;
    font-weight: bold;
    text-align: center;
    background: var(--vh-color-neutral-04);
    color: var(--vh-color-neutral-01);
    border-radius: 100%;
  }

  .search-state {
    margin: 10px;
  }

  .search-results .active {
    border: 1px solid var(--vh-color-neutral-04);
    background-color: var(--vh-color-neutral-02);
    cursor: pointer;
  }

  .search-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-right: 10px;
    cursor: pointer;
  }

  .search-line p {
    padding-left: 30px;
    margin: 10px 0px;
  }

  .search-file-lines {
    font-size: inherit;
    margin-top: 10px;
  }

  .search-file-lines .search-pattern {
    background: var(--vh-color-neutral-04);
    color: var(--vh-color-neutral-01);
    margin: 0.5px;
    padding: 1px;
  }

  .new-btn {
    justify-content: right;
    margin-right: 10px;
    cursor: pointer;
  }

  .utils {
    display: flex;
    gap: 2px;
  }

  .file-or-directory:hover,
  .settings:hover {
    // background-color: #47546b;
    background-color: rgba(255, 255, 255, 0.075);
  }

  .active-file,
  .active-file:hover {
    background-color: #264399;
  }

  .name {
    display: flex;
    text-overflow: ellipsis;
    align-items: center;
    white-space: nowrap;
    overflow: hidden;
    gap: 6px;
    .file-icon,
    .arrow-wrapper {
      opacity: 0.5;
    }
  }

  .rename-input {
    width: 100%;
    border: 0px;
    outline: none;
    background: transparent;
    color: var(--vh-color-neutral-04);
    font-family: var(--vzcode-font-family);
    font-size: inherit;
    font-weight: inherit;
    padding: 0px;
    margin: 0px;
  }

  .indentation {
    margin-left: 20px;
  }

  .settings {
    height: 30px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 14px 0;
    margin: 10px;
    border-radius: 4px;
  }

  /* Empty state for when there are no files */
  .empty {
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 10px;
    padding: 20px;
    border: 1px dashed rgba(255, 255, 255, 0.5);
    border-radius: 10px;
    min-height: 200px;
    position: relative;
    pointer-events: none;

    .empty-text {
      font-size: 16px;
      font-weight: 500;
      pointer-events: none;
      position: absolute;
      text-align: center;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      width: 80%;
      opacity: 1;
      transition: opacity 0.2s ease;

      &:not(:first-child) {
        opacity: 0;
      }
    }

    &.drag-over {
      .empty-text {
        opacity: 0;

        &:last-child {
          opacity: 1;
        }
      }
    }
  }

  .connection-status {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    font-size: 12px;

    .connection-status-indicator {
      width: 16px;
      height: 16px;
      border-radius: 50%;

      &.connected {
        background-color: var(--vh-color-success-01);
      }

      &.pending {
        background-color: var(--vh-color-caution-01);
      }

      &.disconnected {
        background-color: var(--vh-color-warning-01);
      }
    }
  }

  .ai-buttons {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 10px 10px 0 10px;
    border-top: 1px solid var(--vh-color-neutral-02);

    .ai-button {
      padding: 8px 12px;
      border: 1px solid var(--vh-color-neutral-03);
      border-radius: 6px;
      background: var(--vh-color-neutral-01);
      color: var(--vh-color-neutral-04);
      font-size: 12px;
      font-weight: 500;
      font-family: var(--vzcode-font-family);
      cursor: pointer;
      transition: all 0.2s ease;

      i {
        margin-right: 8px;
      }

      &:hover {
        background: var(--vh-color-hover-dark);
        border-color: var(--vh-color-neutral-04);
      }

      &:active {
        background: var(--vh-color-neutral-03);
      }

      &.copy-button {
        border-color: var(--vh-color-primary-03);

        &:hover {
          background: var(--vh-color-hover-dark);
          border-color: var(--vh-color-primary-04);
        }
      }

      &.export-button {
        border-color: var(--vh-color-primary-03);

        &:hover {
          background: var(--vh-color-hover-dark);
          border-color: var(--vh-color-neutral-04);
        }
      }

      &.paste-button {
        border-color: var(--vh-color-success-03);

        &:hover {
          background: var(--vh-color-hover-dark);
          border-color: var(--vh-color-success-04);
        }
      }
    }
  }

  .name {
    display: flex;
    align-items: center;
    white-space: nowrap;
    overflow: hidden;
    gap: 6px;
    position: relative;
  }

  .presence-indicators {
    position: absolute;
    top: 0;
    display: flex;
    gap: 4px;
  }

  .presence-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 8px;
    color: #000000;
    z-index: 1;
  }

  // Visual Editor Slider Styles
  .visual-editor {
    display: flex;
    flex-direction: column;
    gap: 16px;
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
  }

  .visual-editor-slider {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 12px 10px;
    background: var(--vh-color-neutral-01);
    border: 1px solid var(--vh-color-neutral-02);
    border-radius: 8px;
    transition: all 0.2s ease;
    width: 100%;
    box-sizing: border-box;

    &:hover {
      border-color: var(--vh-color-neutral-03);
      background: rgba(255, 255, 255, 0.02);
    }
  }

  .slider-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
  }

  .slider-label {
    font-family: var(--vzcode-font-family);
    font-size: 13px;
    font-weight: 500;
    color: var(--vh-color-neutral-04);
    margin: 0;
  }

  .slider-value {
    font-family: var(--vzcode-font-family);
    font-size: 13px;
    font-weight: 600;
    color: #66ecff; // SKY color from theme
    background: rgba(102, 236, 255, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
    min-width: 40px;
    text-align: center;
  }

  .slider-container {
    position: relative;
    height: 20px;
    display: flex;
    align-items: center;
  }

  .slider-input {
    width: 100%;
    height: 6px;
    background: transparent;
    outline: none;
    border: none;
    cursor: pointer;
    position: relative;
    z-index: 2;

    // Remove default styling
    -webkit-appearance: none;
    appearance: none;

    // Custom track
    &::-webkit-slider-track {
      width: 100%;
      height: 6px;
      background: var(--vh-color-neutral-02);
      border-radius: 3px;
      border: none;
    }

    &::-moz-range-track {
      width: 100%;
      height: 6px;
      background: var(--vh-color-neutral-02);
      border-radius: 3px;
      border: none;
    }

    // Custom thumb
    &::-webkit-slider-thumb {
      -webkit-appearance: none;
      appearance: none;
      width: 18px;
      height: 18px;
      background: linear-gradient(135deg, #66ecff, #00ffff);
      border-radius: 50%;
      cursor: pointer;
      border: 2px solid var(--vh-color-neutral-01);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
      transition: all 0.2s ease;
      position: relative;
      z-index: 3;

      &:hover {
        transform: scale(1.1);
        box-shadow: 0 3px 8px rgba(102, 236, 255, 0.4);
      }

      &:active {
        transform: scale(1.05);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
      }
    }

    &::-moz-range-thumb {
      width: 18px;
      height: 18px;
      background: linear-gradient(135deg, #66ecff, #00ffff);
      border-radius: 50%;
      cursor: pointer;
      border: 2px solid var(--vh-color-neutral-01);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
      transition: all 0.2s ease;

      &:hover {
        transform: scale(1.1);
        box-shadow: 0 3px 8px rgba(102, 236, 255, 0.4);
      }

      &:active {
        transform: scale(1.05);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
      }
    }

    // Focus styles
    &:focus {
      outline: none;

      &::-webkit-slider-thumb {
        box-shadow: 0 0 0 3px rgba(102, 236, 255, 0.3);
      }

      &::-moz-range-thumb {
        box-shadow: 0 0 0 3px rgba(102, 236, 255, 0.3);
      }
    }
  }

  .slider-track-fill {
    position: absolute;
    top: 50%;
    left: 0;
    height: 6px;
    background: linear-gradient(90deg, #77fd8c, #66ecff);
    border-radius: 3px;
    transform: translateY(-50%);
    transition: width 0.1s ease;
    pointer-events: none;
    z-index: 1;
  }

  .slider-bounds {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 2px;
  }

  .min-value,
  .max-value {
    font-family: var(--vzcode-font-family);
    font-size: 11px;
    color: var(--vh-color-neutral-03);
    font-weight: 400;
  }

  // Empty State Component Styles
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 24px 16px;
    margin: 16px;
    background: var(--vh-color-neutral-01);
    border: 1px solid var(--vh-color-neutral-02);
    border-radius: 8px;
    text-align: center;
    font-family: var(--vzcode-font-family);
    font-size: 14px;
    font-weight: 400;
    color: var(--vh-color-neutral-04);
    line-height: 1.5;
    min-height: 80px;

    &:hover {
      border-color: var(--vh-color-neutral-03);
      background: rgba(255, 255, 255, 0.02);
    }
  }
}
