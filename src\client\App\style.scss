@import url('https://fonts.googleapis.com/css2?family=Roboto+Mono:ital,wght@0,100..700;1,100..700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inconsolata:wght@200..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Source+Code+Pro:ital,wght@0,200..900;1,200..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Ubuntu+Mono:ital,wght@0,400;0,700;1,400;1,700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:ital,wght@0,100..800;1,100..800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Fira+Code:wght@300..700&display=swap');
:root {
  // --vzcode-font-family: ui-monospace, SFMono-Regular,
  //   'SF Mono', <PERSON>lo, Consolas, 'Liberation Mono', monospace !important;

  --vzcode-font-family: 'Roboto Mono', monospace !important;
  --vzcode-font-size: 16px;

  /* Variable for the background color */
  --vz-bg-color: rgb(
    41,
    44,
    52
  ); /* Variable for the background color */
}

/* Scrollbar Styles */
::-webkit-scrollbar {
  width: 16px;
  height: 16px;
}

// ::-webkit-scrollbar-track {
//   background: rgb(255, 255, 255, 0.1);
// }

::-webkit-scrollbar-thumb {
  background: rgb(255, 255, 255, 0.3);
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Layout Styles */
html,
body,
#root,
.app {
  height: 100%;
}

.app {
  background-color: var(--vz-bg-color);
  // font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI',
  //   'Noto Sans', Helvetica, Arial, sans-serif,
  //   'Apple Color Emoji', 'Segoe UI Emoji';
  display: flex;

  /* To support Resizer, which is position: absolute */
  position: relative;

  .left,
  .middle {
    display: flex;

    /* To suport PrettierErrorOverlay, which is position: absolute */
    position: relative;
  }

  .middle {
    // flex: 1;
    flex-direction: column;
    min-width: 0;
  }

  .right {
    display: flex;
    flex: 1;
    background-color: white;

    iframe {
      flex: 1;
      border: none;
    }
  }
}
